import Image from "next/image";

export default function RightDetails({ thankyouDetails }) {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { day: "numeric", month: "long", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };

  const formatTime = (timeString) => {
    if (!timeString) {
      return "";
    }
    const [hours, minutes] = timeString.split(":");
    const time = new Date(1970, 0, 1, hours, minutes);
    return time.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
    });
  };
  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const coachGst =
    thankyouDetails?.coach_id?.affiliationType !== "academy"
      ? thankyouDetails?.coachId?.hasGst
        ? subtotal * 0.18
        : 0
      : thankyouDetails?.academy_id?.gstNumber
      ? subtotal * 0.18
      : 0;
  const platformFee = thankyouDetails?.academyId?.platformFee
    ? thankyouDetails.academyId.platformFee / 100
    : 0.12;

  const platformTax = subtotal * platformFee;

  // const platformTax = subtotal * 0.12;
  const taxGST = platformTax * 0.18;
  const total = Math.ceil(subtotal + platformTax + taxGST + coachGst);
  return (
    <div className="mt-4 md:mt-0 md:w-1/2 flex gap-4 flex-col-reverse	md:flex-col">
      <div className="border-b flex flex-col gap-4 pb-4">
        <div className="flex justify-between text-base text-gray-400">
          <p>Subtotal ({thankyouDetails?.courseType})</p>
          <p>₹ {subtotal?.toFixed(2)}</p>
        </div>
        <div className="flex justify-between text-base text-gray-400">
          <p>Platform Fee (12%)</p>
          <p>₹ {platformTax?.toFixed(2)}</p>
        </div>
        <div className="flex justify-between text-base text-gray-400">
          <p>GST (18%)</p>
          <p>₹ {taxGST?.toFixed(2)}</p>
        </div>
        {thankyouDetails?.coachId?.hasGst && (
          <div className="flex justify-between text-base text-gray-400">
            <p>Coach GST (18%)</p>
            <p>₹ {coachGst?.toFixed(2)}</p>
          </div>
        )}
        <div className="flex justify-between text-lg">
          <p>Total</p>
          <p>₹ {total}</p>
        </div>
      </div>
      <div className="bg-white border p-4 rounded">
        <p className="text-[17px] mb-6">Booking Details</p>
        <div className="flex flex-col gap-4 mt-5 text-sm overflow-x-auto max-h-40">
          {thankyouDetails?.classes?.map((x, index) => (
            <div className="flex flex-col gap-2" key={index}>
              <div className="flex justify-between">
                <div className="flex gap-[7px]">
                  <p className="min-w-[3rem] text-gray-500">Dates :</p>
                  <p className="text-gray-500">
                    {`${new Date(x.date).toLocaleDateString("en-IN", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })} (${x?.days})`}
                  </p>
                </div>

                {thankyouDetails?.courseType === "class" ? (
                  <p className="text-gray-500 pr-2">
                    {x?.duration === "30 mins"
                      ? `₹ ${thankyouDetails?.courseId?.fees?.fees30}`
                      : x?.duration === "45 mins"
                      ? `₹ ${thankyouDetails?.courseId?.fees?.fees45}`
                      : x?.duration === "60 mins"
                      ? `₹ ${thankyouDetails?.courseId?.fees?.fees60}`
                      : 0}
                  </p>
                ) : (
                  ""
                )}
              </div>
              <div className="flex gap-[7px]">
                <p className="min-w-[3rem] text-gray-500">Time :</p>
                <p className="text-gray-500">
                  {`${formatTime(x.startTime)} - ${formatTime(x.endTime)}`}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
