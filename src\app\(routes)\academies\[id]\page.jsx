'use client';
import Image from "next/image";
import RecommendedCoaches from "@/components/Homepage/RecommendedCoaches/page";
import TopCategories from "@/components/Homepage/TopCategories/page";
import WhyPeopleLoveKhelSports from "@/components/Homepage/WhyPeopleLoveKhelSports/page";
import Facilities from "@/components/AcademyProfilePage/Facilities/page";
import TrendingScheduleCards from "@/components/AcademyProfilePage/TrendingSchedule/page";
import AcademyCategories from "@/components/AcademyProfilePage/AcademyCategories/page";
import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import Carousel from "react-multi-carousel";
import AcademyImagesCarousel from "@/components/AcademyProfilePage/AcademyImageCarousel/page";
import TopCourses from "@/components/Homepage/TopCourses/page";
import UserIcon from "@/components/ui/UserIcon";

export default function AcademyDetail() {
  const { id } = useParams();
  const [academyInfo, setAcademyInfo] = useState(null);
  const [imageError, setImageError] = useState(false);
  const [blocks, setBlocks] = useState([]); // blocks from API
  const [blockMap, setBlockMap] = useState({}); // { collectionName: blockData }
  const [loading, setLoading] = useState(true);
  // Section data
  const [facilitiesData, setFacilitiesData] = useState([]);
  const [facilitiesLoading, setFacilitiesLoading] = useState(false);
  const [academyCoaches, setAcademyCoaches] = useState([]);
  const [coachesLoading, setCoachesLoading] = useState(false);
  const [testimonialsData, setTestimonialsData] = useState([]);
  const [testimonialsLoading, setTestimonialsLoading] = useState(false);
  const [academyDescription, setAcademyDescription] = useState("");
  const [descriptionLoading, setDescriptionLoading] = useState(false);
  const [topCoursesData, setTopCoursesData] = useState([]);
  const [topCoursesLoading, setTopCoursesLoading] = useState(false);
  const [categoriesData, setCategoriesData] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  // Fetch blocks
  useEffect(() => {
    if (!id) return;
    setLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/blocks`)
      .then(res => res.json())
      .then(res => {
        const arr = Array.isArray(res.data) ? res.data : [];
        setBlocks(arr);
        // Map for quick lookup
        const map = {};
        arr.forEach(b => {
          if (b.blockData && b.blockData.collectionName) {
            map[b.blockData.collectionName] = b.blockData;
          }
        });
        setBlockMap(map);
        setLoading(false);
      })
      .catch(() => {
        setBlocks([]);
        setBlockMap({});
        setLoading(false);
      });
  }, [id]);

  // Fetch academy info (profile image, name, images, etc.)
  useEffect(() => {
    if (!id) return;
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy/${id}`)
      .then(res => res.json())
      .then(response => {
        if (response.status === "success" && response.data) {
          setAcademyInfo(response.data);
        } else {
          setAcademyInfo(null);
        }
      })
      .catch(() => setAcademyInfo(null));
  }, [id]);

  // Fetch facilities if present
  useEffect(() => {
    if (!id || !blockMap.academyFacilities) return;
    setFacilitiesLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/facilities/active`)
      .then(res => res.json())
      .then(data => setFacilitiesData(Array.isArray(data.data) ? data.data : []))
      .catch(() => setFacilitiesData([]))
      .finally(() => setFacilitiesLoading(false));
  }, [id, blockMap.academyFacilities]);

  // Fetch coaches if present
  useEffect(() => {
    if (!id || !blockMap.academyTopCoachCms) return;
    setCoachesLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/coach`)
      .then(res => res.json())
      .then(data => {
        if (data.status === "success" && Array.isArray(data.data)) {
          const transformed = data.data.map(item => ({
            coach: {
              _id: item.coach._id,
              firstName: item.coach.firstName,
              lastName: item.coach.lastName,
              mobile: item.coach.mobile,
              email: item.coach.email,
              status: item.coach.status,
              authStatus: item.coach.authStatus,
              profileImg: item.coach.profileImg || null,
              sportsCategories: item.coach.sportsCategories || [],
              experience: item.coach.experience || 0
            },
            position: item.position
          }));
          setAcademyCoaches(transformed);
        } else {
          setAcademyCoaches([]);
        }
      })
      .catch(() => setAcademyCoaches([]))
      .finally(() => setCoachesLoading(false));
  }, [id, blockMap.academyTopCoachCms]);

  // Fetch testimonials if present
  useEffect(() => {
    if (!id || !blockMap.academyTestimonial) return;
    setTestimonialsLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/testimonials`)
      .then(res => res.json())
      .then(data => setTestimonialsData(Array.isArray(data.data) ? data.data : []))
      .catch(() => setTestimonialsData([]))
      .finally(() => setTestimonialsLoading(false));
  }, [id, blockMap.academyTestimonial]);

  // Fetch description if present
  useEffect(() => {
    if (!id || !blockMap.academyDescription) return;
    setDescriptionLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/description`)
      .then(res => res.json())
      .then(data => setAcademyDescription(data.data?.description || ""))
      .catch(() => setAcademyDescription(""))
      .finally(() => setDescriptionLoading(false));
  }, [id, blockMap.academyDescription]);

  // Fetch top courses if present
  useEffect(() => {
    if (!id || !blockMap.academyTopCourseCms) return;
    setTopCoursesLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/course`)
      .then(res => res.json())
      .then(data => setTopCoursesData(Array.isArray(data.data) ? data.data : []))
      .catch(() => setTopCoursesData([]))
      .finally(() => setTopCoursesLoading(false));
  }, [id, blockMap.academyTopCourseCms]);

  // Fetch categories
  useEffect(() => {
    if (!id) return;
    setCategoriesLoading(true);
    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/academy-cms/${id}/categories`)
      .then(res => res.json())
      .then(data => {
        setCategoriesData(Array.isArray(data.data?.categories) ? data.data.categories : []);
      })
      .catch((error) => {
        console.error("Error fetching categories:", error);
        setCategoriesData([]);
      })
      .finally(() => setCategoriesLoading(false));
  }, [id]);

  // Compose ordered blocks for rendering
  const descriptionBlock = blocks
    .map(b => b.blockData)
    .find(b => b && b.collectionName === 'academyDescription');
  const otherBlocks = blocks
    .map(b => b.blockData)
    .filter(b => b && b.collectionName !== 'academyDescription')
    .sort((a, b) => a.position - b.position);
  const orderedBlocks = [
    ...(descriptionBlock ? [descriptionBlock] : []),
    ...otherBlocks
  ];

  // Show loading state
  if (loading) {
    return (
      <div className="pt-[10px]">
        <div className="pt-[40px] px-[20px] md:px-[40px] mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-center min-h-[400px]">
            {/* Loading spinner */}
            <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mb-6"></div>
            <p className="text-lg text-gray-600">Loading academy details...</p>
          </div>
        </div>
      </div>
    );
  }

  // Check if academy exists
  if (!academyInfo) {
    return (
      <div className="pt-[10px]">
        <div className="pt-[40px] px-[20px] md:px-[40px] mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
              <UserIcon className="w-12 h-12 text-gray-400" />
            </div>
            <h1 className="text-2xl md:text-3xl font-semibold text-gray-800 mb-4">
              Academy Not Found
            </h1>
      
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-[10px]">
      <div className="pt-[40px] px-[20px] md:px-[40px] mx-auto max-w-7xl">
        <div className="w-full flex md:justify-start justify-center">
          {academyInfo?.profileImage && !imageError ? (
            <>
              <Image
                src={academyInfo.profileImage}
                alt="Academy Profile"
                width={600}
                height={200}
                className="block md:hidden w-full h-[200px] object-cover rounded-lg"
                priority
                onError={() => setImageError(true)}
              />
              <Image
                src={academyInfo.profileImage}
                alt="Academy Profile"
                width={237}
                height={140}
                className="hidden md:block w-[237px] h-[140px] object-cover rounded-lg"
                priority
                onError={() => setImageError(true)}
              />
            </>
          ) : (
            <>
              <div className="w-full h-[200px] rounded-lg bg-gray-100 flex md:hidden items-center justify-center text-gray-400">
                <UserIcon className="w-24 h-24" />
              </div>
              <div className="w-[237px] h-[140px] rounded-lg bg-gray-100 hidden md:flex items-center justify-center text-gray-400">
                <UserIcon className="w-16 h-16" />
              </div>
            </>
          )}
        </div>
      </div>
      <div
        className="mt-6 font-semibold px-[20px] md:px-[40px] mx-auto max-w-7xl"
        style={{
          fontFamily: 'Lato, sans-serif',
          lineHeight: '52.27px',
          letterSpacing: '3%',
        }}
      >
        {(() => {
          const name = academyInfo?.name || '';
          const capitalized = name.replace(/\b[a-z]/g, char => char.toUpperCase());
          return <>
            <span className="hidden md:inline" style={{ fontSize: '42px' }}>
              {capitalized}
            </span>
            <span className="md:hidden" style={{ fontSize: '28px' }}>
              {capitalized}
            </span>
          </>;
        })()}
      </div>
      {/* Academy Description */}
      {blockMap.academyDescription && academyDescription && (
        <div className="mx-auto max-w-7xl mt-2 text-justify mb-4 px-[20px] md:px-[40px] text-[16px] md:text-[16px] md:leading-[30.68px] text-[#2B2B2A]" style={{ lineHeight: "30.68px" }}>
          {descriptionLoading ? 'Loading description...' : (
            <div dangerouslySetInnerHTML={{ __html: academyDescription }} />
          )}
        </div>
      )}
      <div className="px-0 md:px-[10px]">
        {academyInfo?.academyImages && academyInfo.academyImages.length > 0 && (
          <AcademyImagesCarousel images={academyInfo.academyImages} />
        )}
      </div>
      
      {/* Academy Categories */}
      {categoriesData.length > 0 && (
        <AcademyCategories
          data={categoriesData}
          blockData={{ title: "Categories" }}
        />
      )}
      
      <div className="mt-1">
        {loading ? (
          <div className="text-center p-10">Loading...</div>
        ) : (
          <>
            {orderedBlocks.map(block => {
              switch (block.collectionName) {
                case 'academyTopCoachCms':
                  return !coachesLoading && academyCoaches.length > 0 && (
                    <RecommendedCoaches
                      key={block.collectionName}
                      data={academyCoaches}
                      blockData={block}
                    />
                  );
                case 'academyFacilities':
                  return facilitiesLoading ? (
                    <div key={block.collectionName} className="text-center p-10">Loading facilities...</div>
                  ) : facilitiesData.length > 0 ? (
                    <Facilities key={block.collectionName} data={facilitiesData} blockData={block} />
                  ) : null;
                case 'academyTestimonial':
                  return !testimonialsLoading && testimonialsData.length > 0 && (
                    <WhyPeopleLoveKhelSports
                      key={block.collectionName}
                      data={testimonialsData}
                      blockData={block}
                    />
                  );
                case 'academyTopCourseCms':
                  return !topCoursesLoading && topCoursesData.length > 0 && (
                    <TopCourses
                      key={block.collectionName}
                      data={topCoursesData}
                      blockData={block}
                    />
                  );
                default:
                  return null;
              }
            })}
          </>
        )}
      </div>
    </div>
  );
}
