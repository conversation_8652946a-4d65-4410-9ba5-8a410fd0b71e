import { useState, useEffect } from "react";
import Image from "next/image";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function AcademyImagesCarousel({ images }) {
  const [academyImages, setAcademyImages] = useState([]);
  
  useEffect(() => {
    if (images && images.length > 0) {
      setAcademyImages(images);
    }
  }, [images]);

  if (!academyImages || academyImages.length === 0) {
    return null;
  }

  return (
    <div className="bg-white">
      <div className="mx-auto max-w-2xl px-4 md:py-16 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
        <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-4 md:mb-6 uppercase">
          Gallery
        </h2>

        <Carousel
          additionalTransfrom={0}
          arrows
          autoPlaySpeed={3000}
          centerMode={false}
          containerClass="container desktopView"
          draggable
          focusOnSelect={false}
          infinite={true}
          itemClass="carousel-item-padding-40-px"
          keyBoardControl
          minimumTouchDrag={80}
          renderButtonGroupOutside={true}
          renderDotsOutside={false}
          responsive={{
            desktop: {
              breakpoint: {
                max: 3000,
                min: 1024,
              },
              items: 3,
              partialVisibilityGutter: 40,
            },
            mobile: {
              breakpoint: {
                max: 464,
                min: 0,
              },
              items: 1.5,
              partialVisibilityGutter: 40,
            },
            tablet: {
              breakpoint: {
                max: 1024,
                min: 464,
              },
              items: 2,
              partialVisibilityGutter: 100,
            },
          }}
          showDots={false}
          sliderClass=""
          slidesToSlide={1}
          swipeable
        >
          {academyImages?.map((imageUrl, index) => (
            <div key={index} className="flex-none">
              <div className="relative w-full overflow-hidden rounded-lg bg-white aspect-h-1 sm:aspect-h-1 sm:aspect-w-2 lg:aspect-h-1 lg:aspect-w-1 group-hover:opacity-75 h-64 sm:h-80">
                <Image
                  src={imageUrl}
                  alt={`Academy image ${index + 1}`}
                  className="h-full w-full object-cover object-center"
                  width={500}
                  height={500}
                />
              </div>
            </div>
          ))}
        </Carousel>
      </div>
    </div>
  );
}