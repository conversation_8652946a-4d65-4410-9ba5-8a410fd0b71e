import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function TopCourses({ data , blockData}) {
  const [homeCourses, setHomeCourses] = useState([]);

  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeCourses(sortedData);
  }, [data]);

  return (
    <div>
      {homeCourses.length > 0 && (
        <div className="bg-white">
          <div className="mx-auto max-w-2xl px-4 md:py-16 py-8 sm:px-6 lg:max-w-7xl lg:px-8">
            <h2 className="md:text-2xl text-[18px] font-bold tracking-tight text-black mb-4 md:mb-6 uppercase">
              {blockData?.title}
            </h2>

            <Carousel
              additionalTransfrom={0}
              arrows
              autoPlaySpeed={3000}
              centerMode={false}
              containerClass="container desktopView"
              draggable
              focusOnSelect={false}
              infinite={true}
              itemClass="carousel-item-padding-40-px"
              keyBoardControl
              minimumTouchDrag={80}
              renderButtonGroupOutside={true}
              renderDotsOutside={false}
              responsive={{
                desktop: {
                  breakpoint: {
                    max: 3000,
                    min: 1024,
                  },
                  items: 4,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
                mobile: {
                  breakpoint: {
                    max: 464,
                    min: 0,
                  },
                  items: 1.5,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
                tablet: {
                  breakpoint: {
                    max: 1024,
                    min: 464,
                  },
                  items: 3,
                  partialVisibilityGutter: 40, // Adjust this value to add space between items
                },
              }}
              showDots={false}
              sliderClass=""
              slidesToSlide={1}
              swipeable

            >
              {homeCourses.map((product, index) =>
                product?.course ? (
                  <div key={index} className="flex-none">
                    <div className="w-full rounded-2xl object-cover">
                      <Link href={`/courses/${product?.course?._id}`}>
                        <Image
                          src={product?.course?.images[0]?.url}
                          alt={product?.course?.courseName}
                          width={500}
                          height={500}
                          className="w-100 h-52 rounded-t-lg object-cover"
                        />
                      </Link>
                    </div>
                    <div className="border rounded-lg p-3 -mt-2 flex flex-col gap-2 md:gap-4">
                      <div className="mt-4 flex justify-between items-center text-base not-italic font-medium">
                        <Link href={`/courses/${product?.course?._id}`}>
                          <p className="text-white px-3.5 py-2 bg-red-600 rounded-lg">
                            {product?.course?.fees?.feesCourse
                              ? `₹ ${Math.round(
                                product?.course?.fees?.feesCourse * 1.1
                              )}`
                              : "Explore"}
                          </p>
                        </Link>
                      </div>
                      <div className="flex flex-col md:gap-4 gap-2">
                    
                        <h3>
                          <Link
                           className="text-[16px] md:text-[18px] not-italic font-medium text-black h-[26px] line-clamp-1"
                          href={`/courses/${product?.course?._id}`}>
                            <span aria-hidden="true" />
                            {product?.course?.courseName}
                          </Link>
                        </h3>
                        <p className="text-[14px] md:text-[16px] font-light text-gray-400 not-italic line-clamp-2" dangerouslySetInnerHTML={{ __html: product?.course?.description }}/>
                        <div className="text-[14px] md:text-[16px] flex items-center underline gap-2">
                          <Link href={`/courses/${product?.course?._id}`}>
                            What will you learn{" "}
                          </Link>
                          <Image
                            src="/Arrow.svg"
                            alt="arrowsvg"
                            width={20}
                            height={20}
                            className="w-4 h-auto object-cover"
                            style={{ width: "auto", height: "auto" }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null
              )}
            </Carousel>
          </div>
        </div>
      )}
    </div>
  );
}
