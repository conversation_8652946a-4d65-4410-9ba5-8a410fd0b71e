import Link from "next/link";
import react, { useEffect, useState, useRef } from "react";
import { useSearchParams } from "next/navigation";
import ReactToPrint from 'react-to-print';

import Invoice from "@/components/Invoice/Invoice";
import CoachInvoice from "@/components/Invoice/CoachInvoice";
import OrderSummary from "@/components/OrderSummary/OrderSummary";
export default function BookingRight({ bookingData }) {
  const bookingDate = new Date(bookingData?.createdAt.split("T")[0]).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' })
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [invoiceShow, setInvoiceShow] = useState("khel");

  const [isCoachInvoice, setIsCoachInvoice] = useState(false);
  const [isOrderSummary, setIsOrderSummary] = useState(false);
  const [isOverflowHidden, setIsOverflowHidden] = useState(false);
  const [bookingIds, setBookingIds] = useState(bookingData?._id)
  const openModal = () => {
    setIsModalOpen(true);
    setIsOverflowHidden(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsOverflowHidden(false);
  };
  const pdref = useRef();
  const params = useSearchParams();
  const [thankyouDetails, setThankyouDetails] = useState();
  let booking_id = params.get("booking_id")
  useEffect(() => {
    setBookingIds(bookingData?._id)
    getUserToken(bookingData?._id);

  }, [bookingData])

  async function getUserToken(bookingIds) {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result = await response.json();
    if (result?.user?.token) {
      getBookingById(result.user.token, bookingIds);
    }
  }
  async function getBookingById(token, bookingIds) {
    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${bookingIds}`, requestOptions)
      .then((response) => response.json())
      .then((result) => setThankyouDetails(result))
      .catch((error) => console.error(error));
  }
  return (
    <div className="md:w-1/2 flex flex-col gap-4">

      <div className={`rounded-md border p-4 flex flex-col gap-[.8rem] ${bookingData?.courseId?.classType !== 'class' ? "h-52 overflow-auto" : ""}`}>

        {bookingData?.classes?.map((bookingTime, index) => {
          const date = new Date(bookingTime?.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
          const endDate = new Date(bookingTime.endDate).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
          return (
            bookingData?.courseId?.classType === 'class' ? (
              <div key={index} className="flex flex-col md:flex-row md:items-center gap-[5px] md:gap-4">
                <p className="w-[6.5rem] ">Booking Slot :</p>
                <div className="flex items-center gap-2">
                  <p className="font-semibold ">{date}</p>
                  <p className=" font-semibold ">|</p>
                  <p className="font-semibold "> {bookingTime.startTime} - {bookingTime.endTime}</p>
                  <p className="font-semibold text-gray-500"> ({bookingTime?.duration})</p>
                </div>
              </div>
            ) : (
              <div key={index} className="flex gap-1 flex-col">
                <div className="flex gap-2">
                  <p className="w-[3.5rem] font-semibold ">Date :</p>
                  <p className="font-semibold">{date} </p>
                </div>

                <div className="flex items-center gap-2">
                  <p className="w-[3.5rem] font-semibold ">Time :</p>
                  <p className="font-semibold ">{bookingTime.startTime} - {bookingTime.endTime} </p>
                </div>
              </div>
            )
          );
        }
        )}
      </div>

      <div className="flex flex-col gap-4 rounded border p-4">

        <div className="flex gap-2 md:pb-0 md:border-none pb-2 border-b-[1px] flex-wrap">
          <p className="min-w-[7rem]">Coach Details : </p>
          <Link href={`/coaches/${bookingData?.courseId?.coach_id}`} className="text-sky-500 underline pointer">

            {bookingData?.coachId?.firstName ? `${bookingData?.coachId?.firstName} ${bookingData?.coachId?.lastName}` : bookingData?.courseId?.coachName}
          </Link>
        </div>

        <div className="flex gap-2 md:pb-0 md:border-none pb-2 border-b-[1px] flex-wrap">
          <p className="min-w-[7rem]">Price : </p>
          <p className="text-gray-500">
            ₹{bookingData?.pricePaid}
            <span className="text-[13px] text-gray-500"> (inclusive all taxes)</span>
          </p>
        </div>

        <div className="flex gap-2 md:pb-0 md:border-none pb-2 border-b-[1px] flex-wrap">
          <p className="min-w-[7rem]">Booking Date : </p>
          <p className="text-gray-500">{bookingDate}</p>
        </div>

        <div className="flex gap-2 md:pb-0 md:border-none pb-2 border-b-[1px] flex-wrap">
          <p className="min-w-[7rem]">Booking ID : </p>
          <p className="text-gray-500">{bookingData?.bookingId}</p>
        </div>

        <div className="flex gap-4">
          <p className={`min-w-[7rem]`}>Venue :</p>
          <p className="text-gray-500 w-full">
            {bookingData?.courseId?.facility?.name || bookingData?.courseId?.facility?.addressLine1}
            {bookingData?.courseId?.facility?.addressLine1 && bookingData?.courseId?.facility?.name ? `, ${bookingData?.courseId?.facility?.addressLine1}` : ""}
            {bookingData?.courseId?.facility?.addressLine2 ? `, ${bookingData?.courseId?.facility?.addressLine2}` : ""}
            {bookingData?.courseId?.facility?.city ? `, ${bookingData?.courseId?.facility?.city}` : ""}
            {bookingData?.courseId?.facility?.country ? `, ${bookingData?.courseId?.facility?.country}` : ""}
          </p>

        </div>

        <div className="flex gap-2 md:pb-0 md:border-none flex-wrap">
          <p className="min-w-[7rem]">Need to Carry : </p>
          <p className="text-gray-500 w-full"
            dangerouslySetInnerHTML={{ __html: bookingData?.courseId?.whatYouHaveToBring }}
          ></p>
        </div>
        <div className="flex flex-col md:flex-row  md:space-y-0 md:space-x-4 items-center">
          <button
            onClick={() => {
              setInvoiceShow("khel");
              openModal();
            }}
            className="text-left text-sm md:text-lg font-bold text-sky-400"
          >
            Platform Invoice
          </button>
          
{bookingData?.coachId?.affiliationType === "academy" ? (
    <button
      onClick={() => {
        setInvoiceShow("coach");
        openModal();
      }}
      className="text-left text-sm md:text-lg font-bold text-sky-400"
    >
      Academy Invoice
    </button>
  ) : (

          <button
            onClick={() => {
              setInvoiceShow("coach");
              openModal();
            }}
            className="text-left text-sm md:text-lg font-bold text-sky-400"
          >
            Coach Invoice
          </button>
            )}
          <button
            onClick={() => {
              setInvoiceShow("orderSummary");
              openModal();
            }}
            className="text-left text-sm md:text-lg font-bold text-sky-400"
          >
            Order Summary
          </button>

        </div>
        {isModalOpen && (
          <div
            className={`fixed top-0 left-0 w-full h-full flex items-center justify-center bg-gray-500 bg-opacity-50 z-50 ${isOverflowHidden ? "overflow-hidden" : ""
              }`}
          >
            <div className="md:w-[60%] w-[95%] bg-white p-6 rounded-lg shadow-lg flex flex-col gap-4 max-h-[500px] lg:max-h-[700px] overflow-y-auto">
              <div ref={pdref}>
                {invoiceShow === "khel" ? (
                  <Invoice bookingIds={bookingIds} />
                ) : invoiceShow === "coach" ? (
                  <CoachInvoice bookingIds={bookingIds} />
                ) : invoiceShow === "orderSummary" ? (
                  <OrderSummary thankyouDetails={thankyouDetails} />
                ) : null}
              </div>
              <div className="flex justify-end gap-4 mt-4 pt-4 border-t border-gray-200">
                <ReactToPrint
                  trigger={() => (
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      Print
                    </button>
                  )}
                  content={() => pdref.current}
                />
                <button 
                  onClick={closeModal}
                  className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}