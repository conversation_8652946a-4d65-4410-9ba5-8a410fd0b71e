import React, { useEffect, useState } from "react";
import "./Invoice.css";
import { useSearchParams } from "next/navigation";
import { State } from "country-state-city";

const CoachInvoice = ({ bookingIds }) => {
  const params = useSearchParams();
  const [thankyouDetails, setThankyouDetails] = useState();
  const [booking_id, setBooking_Id] = useState(
    bookingIds || params.get("booking_id")
  );
  const [gstStateName, setGstStateName] = useState("");
  const [facilityState, setFacilityState] = useState("");

  useEffect(() => {
    if (bookingIds) {
      setBooking_Id(bookingIds);
    }
  }, [booking_id]);

  useEffect(() => {
    if (booking_id) {
      getUserToken(booking_id);
    }
  }, [booking_id]);

  async function getUserToken(booking_id) {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result = await response.json();
    if (result?.user?.token) {
      getBookingById(result.user.token, booking_id);
    }
  }

  async function getBookingById(token, booking_id) {
    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      redirect: "follow",
    };

    fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${booking_id}`,
      requestOptions
    )
      .then((response) => response.json())
      .then((result) => {
        setThankyouDetails(result);

        const isAcademyAffiliated =
          result?.coachId?.affiliationType === "academy";

        // Convert GST state ISO code to full state name
        const gstState = State.getStateByCodeAndCountry(
          isAcademyAffiliated
            ? result?.academyId?.officeAddress?.state
            : result?.coachId?.gstState,
          "IN"
        )?.name;

        const facilityState = isAcademyAffiliated
          ? result?.academyId?.officeAddress?.state
          : result?.courseId?.facility?.state;

        setGstStateName(gstState || "");
        setFacilityState(facilityState || "");
      })
      .catch((error) => console.error(error));
  }

  // Amount breakdown
  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const taxGST = thankyouDetails?.coachId?.hasGst ? subtotal * 0.18 : 0;
  const isSameState = gstStateName === facilityState;
  const cgst = isSameState ? taxGST / 2 : 0;
  const sgst = isSameState ? taxGST / 2 : 0;
  const igst = taxGST;

  let from;
  if (thankyouDetails?.coachId?.affiliationType === "academy") {
    from = `Umn Khel Shiksha Private Limited on behalf of ${thankyouDetails?.academyId?.name}`;
  } else {
    from = `Umn Khel Shiksha Private Limited on behalf of ${thankyouDetails?.coachId?.firstName} ${thankyouDetails?.coachId?.lastName}`;
  }
  let invoiceno = thankyouDetails?.bookingId;
  let date = new Date(thankyouDetails?.createdAt).toLocaleDateString("en-IN", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

  return (
    <div>
      {/* MAIN BOX */}
      <div>
        {/* for heading */}
        <div className="diagonal_main">
          <div className="diagonal_left"></div>
          <div className="diagonal-border"></div>
          <div className="diagonal_right">
            <p className="font-bold text-white text-2xl pt-32 text-end pr-32">
              INVOICE
            </p>
          </div>
        </div>
        {/* FROM AND INVOICE NO BOX */}
        <div className="flex justify-between pl-6 pr-6">
          <div>
            <div className="flex gap-1.5">
              <p className="font-semibold">From :</p>
              <div className=" flex flex-col text-left">
                {from.split(",").map((item, index) => (
                  <p key={index}>{item}</p>
                ))}
              </div>
            </div>
            {thankyouDetails?.coachId?.hasGst && (
              <p className="mt-4">
                GST ID :{" "}
                {thankyouDetails?.coachId?.affiliationType === "academy"
                  ? thankyouDetails?.academyId?.gstNumber
                  : thankyouDetails?.coachId?.gstNumber}
              </p>
            )}
          </div>
          <div className="flex gap-6 ">
            <div className="flex flex-col text-left">
              <p className="font-bold">Invoice No :</p>
              <p className="font-bold">Date :</p>
            </div>
            <div className="flex flex-col text-left">
              <p>{invoiceno}</p>
              <p>{date}</p>
            </div>
          </div>
        </div>
        {/* BILL TO BOX */}
        <div className="flex justify-between pl-6 pr-6 mb-20">
          <div>
            <div className="flex gap-1.5 mt-4">
              <p className="font-semibold">Bill :</p>
              <div className="flex flex-col text-left">
                <p>{thankyouDetails?.playerName}</p>
                <p>{thankyouDetails?.player?.mobile}</p>
                <p>{thankyouDetails?.playerEmail}</p>
              </div>
            </div>
          </div>
        </div>
        {/* DESCRIPTION AND AMOUNT BOX */}
        <div style={{ marginTop: "2%" }}>
          <table>
            <tr style={{ color: "red" }}>
              <th>DESCRIPTION/MEMO</th>
              <th>TOTAL AMOUNT</th>
            </tr>

            <tr style={{ verticalAlign: "top" }}>
              <td>Basic Price</td>
              <td>₹{subtotal?.toFixed(2)}</td>
            </tr>
            {thankyouDetails?.coachId?.hasGst && isSameState && (
              <>
                <tr>
                  <td>CGST(9%)</td>
                  <td>₹{cgst?.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>SGST(9%)</td>
                  <td>₹{sgst?.toFixed(2)}</td>
                </tr>
              </>
            )}
            {thankyouDetails?.coachId?.hasGst && !isSameState && (
              <tr>
                <td>IGST(18%)</td>
                <td>₹{igst?.toFixed(2)}</td>
              </tr>
            )}
            <tr>
              <td style={{ color: "red", fontWeight: "bold" }}>TOTAL AMOUNT</td>
              <td>₹{(subtotal + taxGST).toFixed(2)}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CoachInvoice;
