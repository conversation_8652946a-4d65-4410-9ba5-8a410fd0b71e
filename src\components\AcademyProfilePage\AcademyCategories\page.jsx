import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function AcademyCategories({ data }) {
  const [categories, setCategories] = useState([]);
  
  useEffect(() => {
    if (data && data.length > 0) {
      setCategories(data);
    }
  }, [data]);

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div>
      {categories.length > 0 && (
        <div>
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="mrazx-auto max-w-2xl py-8 sm:py-24 lg:max-w-none md:py-10 lg:py-16">
              <h2 className="md:text-2xl text-[18px] font-bold text-gray-900 uppercase mb-4 md:mb-6">
                Categories
              </h2>
              <div className="">
                <Carousel
                  additionalTransfrom={0}
                  arrows
                  autoPlaySpeed={3000}
                  centerMode={false}
                  containerClass="container"
                  draggable
                  focusOnSelect={false}
                  infinite={true}
                  itemClass="carousel-item-padding-40-px"
                  keyBoardControl
                  minimumTouchDrag={80}
                  renderButtonGroupOutside={false}
                  renderDotsOutside={false}
                  responsive={{
                    desktop: {
                      breakpoint: {
                        max: 3000,
                        min: 1024,
                      },
                      items: 7,
                      partialVisibilityGutter: 40,
                    },
                    mobile: {
                      breakpoint: {
                        max: 464,
                        min: 0,
                      },
                      items: 2.5,
                      infinite: false,
                      partialVisibilityGutter: 30,
                    },
                    tablet: {
                      breakpoint: {
                        max: 1024,
                        min: 464,
                      },
                      items: 2.5,
                      infinite: false,
                      partialVisibilityGutter: 30,
                    },
                  }}
                  showDots={false}
                  sliderClass=""
                  slidesToSlide={1}
                  swipeable
                >
                  {categories &&
                    categories?.map((category, index) =>
                      category ? (
                        <div key={index} className="flex-none">
                          <div className="relative w-full overflow-hidden rounded-lg bg-white aspect-h-1 sm:aspect-h-1 sm:aspect-w-2 lg:aspect-h-1 lg:aspect-w-1 group-hover:opacity-75 h-24 sm:h-36">
                            <Link
                              href={`/categories/${
                                category?.handle
                              }?category=${category?.name?.toLowerCase()}`}
                            >
                              <Image
                                src={category?.image}
                                alt={category?.name}
                                className="h-full w-full object-cover object-center"
                                width={500}
                                height={500}
                              />
                            </Link>
                          </div>
                        </div>
                      ) : null
                    )}
                </Carousel>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 