import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";

export default function WhyPeopleLoveKhelSports({ data , blockData}) {
  const [homeWhyPeopleLoveKhelSports, setHomeWhyPeopleLoveKhelSports] = useState([]);
  useEffect(() => {
    const sortedData = data.sort((a, b) => a.position - b.position);
    setHomeWhyPeopleLoveKhelSports(sortedData);
  }, [data]);
  return (
    <div>
    {homeWhyPeopleLoveKhelSports.length > 0 && (
    <div className="testimonialSec pt-6 pb-[5.5rem]  sm:pt-12 md:pb-[6rem]">
      <div className="mx-auto max-w-7xl px-[16px] md:px-8">
        <div className="md:mb-12 mb-8 mx-auto md:flex md:items-center md:gap-3 md:justify-center max-w-2xl m-auto text-center">
          <h2 className="text-[#FFFFFF] text-[22px] font-bold tracking-tight sm:text-[38px]">
           {blockData?.title}
          </h2>
          {/* <h2 className="text-[#FFFFFF] mt-2 md:mt-0 text-[22px] font-bold tracking-tight sm:text-[38px]">
            people ❤️ Khel Sports
          </h2> */}
        </div>
        <Carousel
          additionalTransfrom={0}
          arrows={false}
          autoPlaySpeed={3000}
          centerMode={false}
          autoPlay
          containerClass="container"
          draggable={false}
          focusOnSelect={false}
          infinite={true}
          itemClass="carousel-item-padding-40-px"
          keyBoardControl
          minimumTouchDrag={80}
          renderButtonGroupOutside={false}
          renderDotsOutside={false}
          responsive={{
            desktop: {
              breakpoint: {
                max: 3000,
                min: 1024,
              },
              items: 3,
              partialVisibilityGutter: 40,
            },
            mobile: {
              breakpoint: {
                max: 464,
                min: 0,
              },
              items: 1,
              partialVisibilityGutter: 30,
            },
            tablet: {
              breakpoint: {
                max: 1024,
                min: 464,
              },
              items: 1,
              partialVisibilityGutter: 30,
            },
          }}
          showDots={true}
          sliderClass=""
          slidesToSlide={3}
          swipeable
        >
          {homeWhyPeopleLoveKhelSports?.map((post, index) => (
            <article
              key={index}
              className="md:w-[95%] md:mr-[20px] md:flex flex-none lg:flex-auto max-w-xl flex-col items-start justify-between bg-white p-6 rounded-[6px] w-full mr-9"
            >
              <div className="group relative">
                <p className="line-clamp-4 font-normal leading-6 text-[14px] md:text-lg md:leading-7 text-gray-600">
                  {post?.description}
                </p>
              </div>
              <div className="relative mt-8 flex items-center gap-x-4">
                {post?.image && (
                  <Image
                    src={post?.image}
                    alt={post?.name || "User image"}
                    width={50}
                    height={50}
                    className="h-10 w-10 rounded-full bg-gray-50"
                  />
                )}
                <div className="text-sm leading-6">
                  <p className="font-semibold text-gray-900">
                    <span className="absolute inset-0" />
                    {post?.name}
                  </p>
                </div>
              </div>
            </article>
          ))}
        </Carousel>
      </div>
    </div>
    )}
    </div>
  );
}
